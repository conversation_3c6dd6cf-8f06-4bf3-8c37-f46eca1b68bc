<template>
  <div class="WenWen">
    <div class="platform-right-body">
      <div class="platform-alert">
        <el-alert type="warning" :closable="false">
          <i class="iconfont ic-horn"></i>
          <MarqueeText :repeat="2" :duration="100" style="z-index: 99">
            <span title="查看详情" @click="disclaimersClick">合规问答生成回复内容不代表中信建投证券股份有限公司的立场和观点，不构成正式的法律意见和建议，相关资料仅供参考。 使用合规问答时请遵守《国家安全法》《网络安全法》《数据安全法》《个人信息保护法》等法律法规。同时，请注意避免泄露商业秘密等信息。</span>
          </MarqueeText>
        </el-alert>
      </div>
      <div class="platform-right-content" ref="contentRef">
        <div class="platform-right-container">
          <div class="platform-session-item">
            <img src="../../../assets/images/wenwen/zxjtTX.svg" alt="" style="width: 30px;height: 30px;"/>
            <div>
              <div class="platform-session-content" style="box-shadow: 0 1px 1px rgba(138, 149, 153, 0.5);">
                <div>
                  您好，我是中信建投智能合规问答助手，很高兴为您服务~~
                </div>
              </div>
            </div>
          </div>
          <div v-for="(item, index) in contentList" :key="'chatContent' + index"
               v-scroll-to-first class="text_message">
            <div class="question_time" v-if="item.showTime">
              <div class="question_time_data">{{ item.createTime | formatDate }}</div>
            </div>
            <div class="platform-session-item" :class="item.questionContent && 'flex-row-reverse'" >
              <div class="platform-session-item__name">
                {{ userName.toUpperCase() }}
              </div>
              <div class="platform-session-content" :class="item.questionContent && 'question'">
                <div style="word-wrap: break-word; word-break: break-all; overflow: hidden;">
                  <div v-html="item.questionContent"></div>
                </div>
                <div class="platform-session-icon">
                  <div class="basic-icon" @click="copyValue(item.questionContent)">
                    <i class="iconfont icon-ic-copy-n"></i>
                    复制
                  </div>
                </div>
              </div>
            </div>
            <div class="platform-session-item m-b-20">
              <div class="platform-session-item__img">
                <img src="../../../assets/images/wenwen/zxjtTX.svg">
              </div>
              <div class="contentCss" style="position: relative;">
                <div class="platform-session-content"
                     :class="[item.answerContent && 'answer']">
                  <div v-if="item.status === 1 || item.status === 3 || item.status === 6" ref="tableParentRef">
                    <div v-if="item.status === 1">
                      <div v-if="!item.answerContent && !item.citeContent">
                        <div>合规问答助手正在回答中...</div>
                        <div v-if="item.think" class="color-99" v-html="item.think[item.think.length-1]"></div>
                      </div>

                      <div v-if="item.citeContent">
                        <div class="thinking-header" @click="toggleThinking(index)">
                          <span v-if="!item.answerContent">
                            <i class="iconfont operate" style="margin-right: 4px;color:#333333;">&#xe2aa;</i>
                            <span style="color:#333333;" class="thinking-title">正在思考...</span>
                             <i style="color:#333333;" class="el-icon-arrow-right thinking-icon"
                                :class="{'expanded': thinkingExpanded[index]}"></i>
                          </span>
                          <span v-else>
                            <i class="iconfont operate" style="margin-right: 4px;color:#CF1A1C;">&#xe2aa;</i>
                            <span style="color:#CF1A1C;" class="thinking-title">思考过程</span>
                            <i style="color:#CF1A1C;" class="el-icon-arrow-right thinking-icon"
                               :class="{'expanded': thinkingExpanded[index]}"></i>
                          </span>
                        </div>
                        <div class="thinking-content" :class="{'collapsed': !thinkingExpanded[index]}">
                          <MarkdownContent :content="item.citeContent" />
                        </div>
                      </div>
                      <MarkdownContent :ref="'answerContent' + index"
                                    :content="item.answerContent"
                                    :highlight="true"
                                    :options="options" />
                    </div>
                    <div v-if="item.status === 6">
                      {{ '已停止生成' }}
                    </div>
                    <div v-if="item.status === 3">
                      {{ '合规问答助手当前正忙，请稍后再试' }}
                    </div>
                  </div>
                  <div v-if="item.status !== 1 && item.status !== 6 && item.status !== 3" class="fontOver">
                    <i class="huihua_don dot1"></i>
                    <i class="huihua_don dot2"></i>
                    <i class="huihua_don dot3"></i>
                  </div>
                  <div class="ck_css" title="查看数据"
                       v-if="item.status === 1 && item.end && item.dataOrder && item.dataOrder.length > 0"
                       @click="seeClick(item)">
                    <svg v-show="item.seeDataFlag" width="20" height="20" viewBox="0 0 20 20" fill="none" xmlns="http://www.w3.org/2000/svg">
                      <path d="M9.00001 5.46362C9.71895 5.46362 10.4766 5.64644 11.2535 6.00679C11.9338 6.32319 12.6316 6.77671 13.3277 7.35503C14.3068 8.1689 15.075 9.05483 15.5338 9.63315C15.075 10.2115 14.3051 11.0974 13.3277 11.9113C12.6316 12.4896 11.9338 12.9431 11.2535 13.2595C10.4783 13.6199 9.71895 13.8027 9.00001 13.8027C8.28106 13.8027 7.52344 13.6199 6.74649 13.2595C6.06622 12.9431 5.36837 12.4896 4.67227 11.9113C3.69317 11.0974 2.92501 10.2115 2.46622 9.63315C2.92501 9.05483 3.69493 8.1689 4.67227 7.35503C5.36837 6.77671 6.06622 6.32319 6.74649 6.00679C7.52344 5.64644 8.28106 5.46362 9.00001 5.46362ZM9.00001 4.33862C5.16622 4.33862 1.96876 8.39038 1.2252 9.4064C1.12501 9.5435 1.12501 9.72808 1.2252 9.86343C1.96876 10.8794 5.16622 14.9312 9.00001 14.9312C12.8338 14.9312 16.0313 10.8794 16.7748 9.86343C16.875 9.72632 16.875 9.54175 16.7748 9.4064C16.0313 8.39038 12.8338 4.33862 9.00001 4.33862Z" fill="#CF1A1C"/>
                      <path d="M9.00001 7.36733C9.60645 7.36733 10.176 7.60288 10.6031 8.03179C11.032 8.46069 11.2676 9.03022 11.2676 9.63491C11.2676 10.2396 11.032 10.8109 10.6031 11.238C10.176 11.6669 9.60645 11.9025 9.00001 11.9025C8.39356 11.9025 7.82403 11.6669 7.39688 11.238C6.96797 10.8109 6.73243 10.2414 6.73243 9.63491C6.73243 9.02847 6.96797 8.45894 7.39688 8.03179C7.82403 7.60288 8.39356 7.36733 9.00001 7.36733ZM9.00001 6.24233C7.12618 6.24233 5.60743 7.76108 5.60743 9.63491C5.60743 11.5087 7.12618 13.0275 9.00001 13.0275C10.8738 13.0275 12.3926 11.5087 12.3926 9.63491C12.3926 7.76108 10.8738 6.24233 9.00001 6.24233Z" fill="#CF1A1C"/>
                    </svg>
                    <svg v-show="!item.seeDataFlag" width="20" height="20" viewBox="0 0 20 20" fill="none"
                         xmlns="http://www.w3.org/2000/svg">
                      <path
                          d="M5.0957 12.4512C4.05469 11.5703 3.23438 10.623 2.74023 10C3.25 9.35742 4.10547 8.37305 5.19141 7.46875C5.96484 6.82617 6.74023 6.32227 7.49609 5.9707C8.35742 5.57031 9.20117 5.36719 10 5.36719C10.5938 5.36719 11.2129 5.48047 11.8457 5.70312L12.8086 4.74023C11.9258 4.35742 10.9805 4.11914 10 4.11914C5.74024 4.11914 2.1875 8.62109 1.36133 9.75C1.25 9.90234 1.25 10.1074 1.36133 10.2578C1.75586 10.7988 2.78125 12.1172 4.20898 13.3418L5.0957 12.4512ZM18.6387 9.74609C18.1758 9.11133 16.8516 7.41406 15.0254 6.04688L16.9824 4.08984C17.2266 3.8457 17.2266 3.44922 16.9824 3.20508C16.7383 2.96094 16.3418 2.96094 16.0977 3.20508L4.13281 15.1719C3.88867 15.416 3.88867 15.8125 4.13281 16.0566C4.25391 16.1777 4.41406 16.2402 4.57422 16.2402C4.73438 16.2402 4.89453 16.1797 5.01562 16.0566L6.26758 14.8047C7.40039 15.4453 8.66406 15.8867 9.99805 15.8867C14.2578 15.8867 17.8105 11.3848 18.6367 10.2559C18.7481 10.1035 18.748 9.89648 18.6387 9.74609ZM12.5195 10C12.5195 10.6738 12.2578 11.3066 11.7813 11.7812C11.3066 12.2578 10.6738 12.5195 10 12.5195C9.58789 12.5195 9.19141 12.4219 8.83594 12.2363L12.2363 8.83594C12.4219 9.19141 12.5195 9.58789 12.5195 10ZM14.8086 12.5312C14.0352 13.1738 13.2598 13.6777 12.5039 14.0293C11.6426 14.4297 10.7988 14.6328 10 14.6328C9.20117 14.6328 8.35938 14.4297 7.49609 14.0293C7.39453 13.9824 7.29297 13.9316 7.19141 13.8789L7.92383 13.1465C8.51953 13.5391 9.23242 13.7695 10 13.7695C12.082 13.7695 13.7695 12.082 13.7695 10C13.7695 9.23242 13.541 8.51953 13.1465 7.92383L14.1289 6.94141C14.3555 7.10547 14.5801 7.28125 14.8066 7.46875C15.8945 8.37305 16.748 9.35742 17.2578 10C16.75 10.6426 15.8945 11.627 14.8086 12.5312Z"
                          fill="#999999"/>
                      <path
                          d="M10 7.48047H10.0664L11.1406 6.40625C10.7813 6.29297 10.3984 6.23047 10 6.23047C7.91797 6.23047 6.23047 7.91797 6.23047 10C6.23047 10.3984 6.29297 10.7813 6.40625 11.1406L7.48047 10.0664V10C7.48047 9.32617 7.74219 8.69336 8.21875 8.21875C8.69336 7.74219 9.32617 7.48047 10 7.48047Z"
                          fill="#999999"/>
                    </svg>
                  </div>
                  <div class="platform-session-anniu" v-if="item.end && item.status === 1 && item.answerContent !== ''">
                    <div class="fz_css" @click="handleCopy(index)">
                      <i class="icon iconfont icon-ic-copy-n" :title="'复制'" style="font-size: 18px"><span
                          class="anniu_title"></span></i>
                    </div>
                    <div class="cai_css" @click="supportCopy(item.id, item.feedbackType, 1,index)">
                      <i class="icon iconfont icon-ic-cai-o" :title="'踩'"
                         :style="{'color':(item.feedbackType === '1'?'#CF1A1C':'')}"
                         style="font-size: 18px"><span class="anniu_title"></span></i>
                    </div>
                    <div class="zan_css" @click="supportCopy(item.id, item.feedbackType, 0,index)">
                      <i class="icon iconfont icon-ic-zan-o" :title="'赞'"
                         :style="{'color':(item.feedbackType === '0'?'#CF1A1C':'')}"
                         style="font-size: 18px"><span class="anniu_title"></span></i>
                    </div>
                    <div>
                      <i class="icon iconfont el-icon-chat-dot-square" :title="'跳转H5'" style="font-size: 18px"
                         @click="goH5(item.id)"><span class="anniu_title"></span></i>
                    </div>
                  </div>
                </div>

                <div v-if="item.end && item.status === 1 && item.answerContent !== ''" class="zhenbie">内容由AI生成，请仔细甄别</div>
              </div>
            </div>
            <div v-if="item.status === 6" class="answerTip" style="white-space: nowrap;">该会话已停止生成</div>
          </div>
        </div>
      </div>
      <div class="platform-right-footer">
        <div class="platform-footer-body">
          <div style="display: flex;position: relative">
            <div class="colCss bhv_log" v-if="contentList.length > 0">
              <el-button type="info" style="width: 100%;height: 30px;margin-top: 2px" plain
                         @click="createChat('新对话')"><i
                  class="iconfont ic-plus" style="color: #333333"></i>&nbsp;新建对话
              </el-button>
            </div>
            <div class="colCss" style="top: 2px;position: relative;margin-left: 12px;">
              <el-button type="info" class="button_tzsc" plain
                         v-if="status === 1 && stopFlag && contentList[contentList.length - 1].id" @click="handleStop">
<!--                <img src="../../../assets/images/wenwen/Rectangle.png" width="10px" height="10px" alt="">-->
                停止生成
              </el-button>
              <el-button type="info" class="button_ccsc" plain v-if="status === 2 && !stopFlag" @click="continueBorn">
                重新生成
              </el-button>
            </div>
          </div>
          <el-row :gutter="24" style="display: flex;margin-bottom: 10px;position: relative">
            <div style="float: right;position: absolute;right: 35px;top: -40px">
            </div>
          </el-row>
          <div class="platform-footer-main">
            <div class="platform-footer-send"
                 @mouseenter="inputHoving = true"
                 @mouseleave="inputHoving = false">
              <div class="platform-footer-send__content">
                <div class="platform-footer-send__textarea">
                  <div class="platform-footer-send-div">
                    <div>
                      <el-input ref="inputContent" placeholder="来说点什么吧..." @focus="handleFocus"
                                @blur="visible = false"
                                @input="handleInput"
                                v-model="sendContent" type="textarea" :autosize="{maxRows: 3}"
                                @keydown.enter.native="enterClick($event)"
                                :class="{'stateInput': inputLength}"
                                class="inpTextarea"
                                :maxlength="1000" clearable></el-input>
                    </div>
                    <div class="platform-footer-send__number"
                         :class="textNumber === 0 && 'hidden'" v-show="inputLength"><span
                        :style="{'color': textNumber === 1000 ?'red':''}">{{ textNumber }}</span> /1000
                    </div>
                  </div>
                </div>
                <div class="platform-footer-send__button send__button_all" @click="getmianze()"
                     :class="{'disabled':homeSending,'butSendTop':inputLength}">
                  <i class="iconfont icon-ic-send-o"></i>
                  <span>发送</span>
                </div>
              </div>
            </div>
          </div>
          <div class="hint">
            <!--内容由 AI 大模型生成，仅供您参考，请遵守<span @click="disclaimersClick" style="cursor: pointer">《AI综合服务平台声明》</span>-->
            <ai-disclaimers></ai-disclaimers>
          </div>
        </div>
      </div>
    </div>
    <disclaimers-new :showDialog="showDialog" :first-flag="true" @close="closeDis"></disclaimers-new>
  </div>
</template>
<script>
import {
  _getFeedbackType,
  _getChatContent,
  _getMaterial,
  _getContainsCompanyCodeOrName,
  getTenantInfo
} from "@/api/chat";
import { TypeWriterQueue } from '@/utils/typeWriter.js'
import Cookies from 'js-cookie';
import MarqueeText from 'vue-marquee-text-component';
import {parseTime} from "@/utils";
import Vue from "vue";
import DisclaimersNew from "@/views/compliance/wenwen/disclaimersNew.vue";
import AiDisclaimers from "@/views/compliance/wenwen/aiDisclaimers.vue";
import {mapGetters, mapMutations} from "vuex";
import {fetchEventSource} from '@microsoft/fetch-event-source';
import {_getChatLoginRecord} from "@/api/Records";
import MarkdownContent from '@/components/MarkdownContent/index.vue'

Vue.directive('scroll-to-first', {
  inserted: function (el) {
    el.scroll()
  }
})
export default {
  components: {
    MarkdownContent,
    DisclaimersNew,
    AiDisclaimers,
    MarqueeText
  },
  // mixins: [common],
  props: {
    currentChat: String,
    answerLoading: Boolean
  },
  filters: {
    formatDate(value) {
      const date = new Date(value)
      const year = date.getFullYear()
      const month = date.getMonth() + 1
      const day = date.getDate()
      const hour = date.getHours() < 10 ? '0' + date.getHours() : date.getHours();
      const minute = date.getMinutes() < 10 ? '0' + date.getMinutes() : date.getMinutes();
      // const second = date.getSeconds() < 10 ? '0' + date.getSeconds() : date.getSeconds();
      return `${year}年${month}月${day}日 ${hour}:${minute}`
    }
  },
  data() {
    return {
      userName: '',
      options: {
        html: true,
        linkify: true
      },
      visible: false, // 发送输入框是否是focus状态
      inputHoving: false, // 发送输入框是否是焦点状态
      firstFlag: false,
      contentList: [],
      status: 0, // 0 1正在回答中或者是回答成功; // 当前状态是停止生成，隐藏回答loading 345对应回答错误的返回提示
      sendContent: '', // 发送输入框的文本
      seeDataFlag: false,
      contentId: '',
      question: '',
      stopFlag: false,
      showDialog: false,
      heartbeatInterval: null,
      currentTime: 0, // 判断是否连续5分钟内会话
      currentTimeTen: 0, // 判断是否连续10分钟内会话
      chatTimeFlag: true, // 用来判断是否需要重新记录时间
      indexNum: 1,
      min5flag: true, // 判断是否连续5分钟内会话
      min10flag: false, // 判断是否连续10分钟内会话
      inputLength: false,
      thinkingExpanded: {}, // 用于存储每个思考内容的展开状态
      isUserScrolling: false, // 用户是否正在查看历史内容
      token: '',
      controller: null,
      contentWriterQueue: null,
      thinkWriterQueue: null,
    }
  },
  computed: {
    // 计算发送输入框输入的文本字数
    textNumber() {
      return this.sendContent.length;
    },
    ...mapGetters(['wenChatGpt', 'wenChatGptName', 'wenBelongsPlate', 'wenNullList', "homeSending", 'checkboxTwo'])
  },
  mounted() {
    this.mianze()
    this.getChatContent();
    // 添加滚动监听
    const dom = this.$refs.contentRef;
    if (dom) {
      dom.addEventListener('scroll', this.handleScroll);
    }
    this.userName = JSON.parse(window.localStorage.getItem("platform_jurisdictionData")).userName.charAt(0)
    getTenantInfo().then(res => {
      this.token = res.data.result.token
    })
  },
  beforeDestroy() {
    this.handleStop()
    this.SET_HOME_SENDING(false)
    // 移除滚动监听
    const dom = this.$refs.contentRef;
    if (dom) {
      dom.removeEventListener('scroll', this.handleScroll);
    }

  },
  methods: {
    ...mapMutations(['SET_HOME_SENDING']),
    goH5(id) {
      window.location.href = `https://services.easy-board.com.cn/ui/chatDong/compliance/material?id=${id}` +
          `&access_token=${this.token}` + '&platform=fromzxjt'
    },
    toggleThinking(index) {
      this.$set(this.thinkingExpanded, index, !this.thinkingExpanded[index]);
    },
    async mianze() {
      const userType = JSON.parse(window.localStorage.getItem("platform_jurisdictionData")).userType
      if (userType !== "0") {
        _getChatLoginRecord().then(res => {
          if (res.data.result) {
            this.showDialog = true
          }
        })
      }
    },
    handleInput() {
      this.$nextTick(() => {
        const textarea = this.$refs.inputContent.$el.querySelector('textarea');
        const content = textarea.value;
        const lines = content.split('\n');
        let lineCount = lines.length;

        // 对于每一行，检查是否超过了文本域的宽度
        for (let line of lines) {
          const lineWidth = this.getTextWidth(line, textarea);
          const textareaWidth = textarea.clientWidth;
          if (lineWidth > textareaWidth) {
            // 计算这一行可以容纳多少个字符
            const charWidth = lineWidth / line.length;
            const maxCharsPerLine = Math.floor(textareaWidth / charWidth);
            lineCount += Math.ceil(line.length / maxCharsPerLine) - 1;
          }
        }
        this.inputLength = lineCount >= 2;
      });
    },
    getTextWidth(text, element) {
      const canvas = document.createElement('canvas');
      const context = canvas.getContext('2d');
      context.font = window.getComputedStyle(element).font;
      return context.measureText(text).width;
    },
    sendMessage(message) {
      this.controller = new AbortController()
      this.contentWriterQueue = new TypeWriterQueue({
        fun: (text) => {
          this.contentList[this.contentList.length - 1].answerContent += text;
        }
      })
      this.thinkWriterQueue = new TypeWriterQueue({
        fun: (text) => {
          this.contentList[this.contentList.length - 1].citeContent += text;
        }
      })
      fetchEventSource('/platform/chat/message/stream', {
        signal: this.controller.signal,
        openWhenHidden: true,
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          'Authorization': this.$store.state.app.token,
          'X-Tenant-Info': this.$store.state.app.info
        },
        // 生成一个随机数
        body: JSON.stringify({...message}),

        onopen(response) {
          if (response.status !== 200) {
            this.controller.abort()
          }
        },

        onmessage: (msg) => {
          const data = JSON.parse(msg.data)
          if (data.error) {
            this.controller.abort()
          }
          const date = new Date()
          console.log('接收消息时间: ' + date.toTimeString() + ' ' + date.getMilliseconds())
          if (data.type === 'answer_content') {
            this.chatSuccess(data)
          } else if (data.type === 'reasoning_content') {
            this.chatReasoning_content(data)
          } else if (data.type === 'link_error') {
            this.socketErrChat()
          }
        },
        onerror(err) {
          this.controller.abort()
        }
      }, 60000);
    },
    chatReasoning_content(data) {
      if (data.think == null) return;
      this.contentList[this.contentList.length - 1].status = 1;
      this.contentList[this.contentList.length - 1].id = data.id;
      // 初始化 citeContent 如果为空
      if (!this.contentList[this.contentList.length - 1].citeContent) {
        this.contentList[this.contentList.length - 1].citeContent = '';
      }
      this.thinkWriterQueue.addToQueue(data.think);
      // this.contentList[this.contentList.length - 1].citeContent += data.think;
      // 初始状态设置为展开，但不强制保持展开
      if (this.thinkingExpanded[this.contentList.length - 1] === undefined) {
        this.$set(this.thinkingExpanded, this.contentList.length - 1, true);
      }
      this.$forceUpdate();
      this.scrollBottom();
    },
    disclaimersClick() {
      this.showDialog = true;
    },
    closeDis() {
      this.firstFlag = false;
      Cookies.set('readcomplete', 'ok');
      this.showDialog = false
    },
    // 赞踩状态
    supportCopy(id, type, num, index) {
      console.log("踩死你1", id)
      console.log("踩死你2", type)
      console.log("踩死你3", num)
      console.log("踩死你4", index)
      let feedbackType = "";
      if ((this.getValue(type) === '' && num === 0) || (type === '1' && num === 0)) {
        feedbackType = "0";
      }
      if ((this.getValue(type) === '' && num === 1) || (type === '0' && num === 1)) {
        feedbackType = "1";
      }
      _getFeedbackType({feedbackType: feedbackType, id: id}).then(res => {
        if (res.data.success) {
          this.contentList[index].feedbackType = res.data.result;
          let list = JSON.stringify(this.contentList);
          this.contentList = JSON.parse(list);
        }
      });
    },
    // 查询聊天记录 -- 发送信息返回结果--保存聊天内容 -------start
    // 获取单个聊天的内容
    getChatContent(type) {
      // 从缓存中获取用户是否阅读过条款
      const readcomplete = Cookies.get('readcomplete')
      if (this.getValue(readcomplete) === '') {
        this.firstFlag = true;
      }
      if (this.getValue(this.currentChat) === '') {
        this.getHistoryListById(null, "", true);
        this.contentList = []
        return;
      }
      _getChatContent({
        chatId: this.currentChat
      }).then(res => {
        this.stopFlag = false
        this.status = 0; // 发送中
        if (res.data.success) {
          this.contentList = res.data.result.chatContentList; // 查询当前会话历史消息
          this.contentList.forEach(item => {
            item.end = true
            if (item.status === 0){
              item.status = 6
            }
          })
          this.showChatTime(this.contentList, false);
          if (this.contentList.length > 0) {
            this.contentList[this.contentList.length - 1].seeDataFlag = true; // 将消息列表中所有小眼睛图标至于false(最后一个睁开，通过点击切换)
          }
          // 默认聊天记录展示最后一条消息列表数据
          if (this.contentList.length > 0) {
            this.getHistoryListById(this.contentList[this.contentList.length - 1].id, this.contentList[this.contentList.length - 1].questionContent, false, null, null, this.contentList[this.contentList.length - 1].dataOrder);
          } else {
            this.getHistoryListById(null, "", true);
          }
          this.scrollBottom(); // 滚动到底部
          this.$nextTick(() => {
            // 初始化所有思考内容为展开状态
            this.contentList.forEach((_, index) => {
              this.$set(this.thinkingExpanded, index, true);
            });
            this.scrollBottom() // 滚动到底部
          })
        }
      });
    },
    showChatTime(chatContentList, sendContent) {
      if (chatContentList == null || chatContentList.length === 0) {
        return;
      }
      if (sendContent === false) {
        let previousChatTime = new Date('1900-01-01 00:00:00');
        for (let index in chatContentList) {
          const date = new Date(chatContentList[index].createTime);
          const contentTime = this.showChatContentTime(index, date, previousChatTime);
          if (contentTime != null) {
            previousChatTime = contentTime;
          }
        }
      } else {
        const date = new Date();
        const contentIndex = chatContentList.length - 2;
        if (contentIndex < 0) {
          let previousChatTime = new Date('1900-01-01 00:00:00');
          this.showChatContentTime(0, date, previousChatTime);
          return;
        }
        let contentListElement = chatContentList[contentIndex];
        let previousChatTime = new Date(contentListElement.createTime);
        const contentTime = this.showChatContentTime(chatContentList.length - 1, date, previousChatTime);
        if (contentTime != null) {
          previousChatTime = contentTime;
        }
      }
    },
    showChatContentTime(index, date, previousChatTime) {
      const number = date - previousChatTime;
      this.$set(this.contentList[index], 'showTime', false)
      if (number > 5 * 60 * 1000 && this.min5flag) {
        this.contentList[index].showTime = true;
        previousChatTime = date;
      } else {
        this.min5flag = false
        this.min10flag = true
      }
      if (number > 10 * 60 * 1000 && this.min10flag) {
        this.min5flag = true
        this.min10flag = false;
        this.contentList[index].showTime = true;
        previousChatTime = date;
      }
      if (this.contentList[index].showTime) {
        return date;
      }
    },
    // 滚动到底部
    scrollBottom() {
      // 如果用户正在查看历史内容，不执行自动滚动
      if (this.isUserScrolling) return;
      this.$nextTick(() => {
        const dom = this.$refs.contentRef;
        if (dom) {
          if (dom.scrollHeight > dom.clientHeight) {
            dom.scrollTop = dom.scrollHeight;
          }
        }
      });
    },
    // 快捷发起聊天
    fastStartChat(name) {
      this.sendContent = name;
      this.getmianze();
    },
    getmianze(){
      const userType = JSON.parse(window.localStorage.getItem("platform_jurisdictionData")).userType
      if (userType != "0") {
        this.mianze().then(this.isCompanyName())
      }else {
        this.isCompanyName()
      }

    },
    isCompanyName() {
      _getContainsCompanyCodeOrName({question:this.sendContent}).then(res => {
        if (res.data.success) {
          if (res.data.result.length > 0) {
            this.$confirm('检测到问题中存在客户信息，是否进行提问？', '提示', {
              confirmButtonText: '继续提问',
              cancelButtonText: '取消',
              type: 'warning'
            }).then(() => {
              this.startChat()
            }).catch(() => {
              this.$message({
                type: 'info',
                message: '已取消'
              });
            });
          } else {
            this.startChat()
          }
        }
      });
    },
    // 发起聊天
    startChat() {
      this.isUserScrolling = false
      this.inputLength = false
      if (this.sendContent.length === 0) {
        this.$message.info("请输入问题")
        return;
      }
      if (this.homeSending) return;
      this.sendContent = this.trimStr(this.sendContent);
      if (this.sendContent.length === 0) return;
      this.$emit('wenStartChatLoading', true)
      // 正常发起一个聊天
      this.sendUserBehaviorLog('发送');
      if (this.currentChat) { // 选择的会话。如果 不为null，则查询数据
        if (this.contentList.length === 0) {
          let name = {
            id: this.currentChat,
            chatName: this.sendContent
          }
          this.$emit('editChatSure', name);
        }
        this.sendChatContent(this.sendContent.replace(/\r|\n|\\s/g, ""));
      } else { // 先建立一个聊天再发起聊天
        this.$emit('getWenCreateChat', "新对话", this.sendContent.replace(/\r|\n|\\s/g, ""), 1, 0);
      }
    },
    // 发起一个聊天(返回问答)
    sendChatContent(name, recordID) {
      this.currentTime = 0;
      let param = {
        createTime: parseTime(new Date()), // 会话时间
        answerContent: '',
        questionContent: name, // 发送到问题
        status: 0,
        think: [],
        aiType: this.wenChatGpt
      };
      this.SET_HOME_SENDING(true)
      const currentExpanded = {...this.thinkingExpanded};
      if (this.contentList === null || this.contentList === undefined ||
          !(this.contentList instanceof Array) || this.contentList.length === 0) {
        this.contentList = [];
        this.$set(this.contentList, 0, param);
      } else {
        this.contentList.push(param);
      }
      // 为新的问题设置展开状态为true,同时保持原有问题的展开状态
      this.thinkingExpanded = {
        0: true, // 新问题默认展开
        ...Object.keys(currentExpanded).reduce((acc, key) => {
          acc[parseInt(key) + 1] = currentExpanded[key]; // 原有的状态往后移一位
          return acc;
        }, {})
      };
      // 滚动到底部
      this.scrollBottom();
      this.status = 1;
      this.sendContent = '';
      // 调用接口
      this.chat(name, recordID); // 查询结果  返回
    },
    // 一个字一个字显示文本
    showText(text) {
      // this.contentList[this.contentList.length - 1].answerContent += text;
      this.contentWriterQueue.addToQueue(text);
      this.scrollBottom(); // 滚动到底部
      this.$forceUpdate();
    },
    // 聊天处理
    chat(questionContent, recordID) {
      this.stopFlag = true;
      this.status = 1; // 发送中
      this.SET_HOME_SENDING(true)
      let param = {
        question: questionContent,
        recordId: this.currentChat || recordID,
        aiTypeName: this.wenChatGptName, // 对应后台模型
        aiType: this.wenChatGpt, // 对应后台模型
        belongsPlate: this.wenBelongsPlate, // 归属板块
        continuousChat: this.checkboxTwo === true ? '0' : '1',
        origin: 'zxjt01',
        historyMessages: [],
        link: Math.random()
      }
      this.showChatTime(this.contentList, true);
      this.sendMessage(param)
    },
    chatSuccess(data) {
      if (!this.stopFlag) {
        return
      }
      this.contentId = data.id
      this.contentList[this.contentList.length - 1].id = data.id
      this.contentList[this.contentList.length - 1].status = 1
      if (data.isEnd) {
        this.contentList[this.contentList.length - 1].tableEndFlag = true
        if (data.dataOrder && data.dataOrder.length > 0) {
          this.contentList[this.contentList.length - 1].dataOrder = data.dataOrder
        }
        this.contentSeeFlag(this.contentId, '', true, 0)
        this.getHistoryListById(this.contentId, this.contentList[this.contentList.length - 1].questionContent, false, this.wenBelongsPlate, "last", data.dataOrder); // 返回answer,根据当条内容id查询资料列表。
        this.stopFlag = true
        this.contentList[this.contentList.length - 1].end = true
        this.SET_HOME_SENDING(false)
        this.status = 2 // 成功
        this.contentId = null
        this.$nextTick(() => {
          this.scrollBottom() // 滚动到底部
        })
        this.$forceUpdate()
        return
      }
      this.showText(data.content)
    },
    socketErrChat() {
      this.$confirm('连接异常中断，请刷新页面后重试', '提示', {
        confirmButtonText: '确定',
        showCancelButton: false,
        type: 'error'
      }).then(() => {
        this.$router.go(0)
      });
    },
    // 停止生成
    handleStop() {
      this.contentList[this.contentList.length - 1].status = 6;
      this.contentList[this.contentList.length - 1].answerContent = '已停止生成'
      this.status = 2;
      this.stopFlag = false;
      this.SET_HOME_SENDING(false)
      this.scrollBottom();
      this.controller.abort()
      this.$emit('wenStartChatLoading', false)
    },
    // 重新生成
    continueBorn() {
      this.sendContent = this.contentList[this.contentList.length - 1].questionContent;
      // 显示停止生成按钮
      this.status = 1;// 发送按钮不可用
      this.stopFlag = false;
      this.SET_HOME_SENDING(false)
      // 获取上一次问答的问题
      this.getmianze(); // 再问一遍
    },
    // 查询聊天记录 -- 发送信息返回结果--保存聊天内容 -------end

    // 发送输入框获取焦点
    handleFocus() {
      this.visible = true;
    },
    // 回车事件
    enterClick(event) {
      if (!event.shiftKey && event.keyCode === 13) {
        event.cancelBubble = true;
        event.stopPropagation();
        event.preventDefault();
        this.getmianze();
      }
    },
    // 新建会话按钮
    createChat(name) {
      if (this.homeSending) {
        this.$message.info('正在回答中，请勿新建对话。');
      } else {
        this.status = 0
        this.$emit('create-chat', name, name)
      }
    },
    // 复制
    handleCopy(index) {
      if (this.$refs['answerContent' + index]) {
        const el = document.createElement('input');
        // 直接使用 answerContent 的原始内容
        let answerContent = this.contentList[index].answerContent || '';
        el.setAttribute('value', answerContent.trim());
        document.body.appendChild(el);
        el.select();
        document.execCommand('copy');
        document.body.removeChild(el);
        this.$message.success('复制成功');
      }
    },
    // 小眼睛切换
    seeClick(row) {
      this.contentSeeFlag(row.id, row.questionContent, !row.seeDataFlag, null, row.dataOrder);
      // 如果都是false 则右侧展开空白页面
      const hasTrueValue = this.contentList.some(item => item.seeDataFlag === true);
      if (!hasTrueValue) {
        this.getHistoryListById(null, "", false);
      }
    },
    contentSeeFlag(id, question, flag, type, dataOrder) { // type==0不需要单挑查列表
      this.contentList.map((data) => { // 用来将小眼睛对应展示情况
        if (data.id === id) {
          data.seeDataFlag = flag;
          if (flag && type !== 0) {
            this.getHistoryListById(id, question, false, null, null, dataOrder);
          }
        } else {
          data.seeDataFlag = false;
        }
        return data;
      });
      // 转一遍json,就可以深度监听数组里面值的变化，要不最后一个小眼睛监听不到变化
      let contentListJson = JSON.stringify(this.contentList);
      this.contentList = JSON.parse(contentListJson);
    },
    // 打开聊天详情页面列表
    getHistoryListById(contentId, question, clearFlag, plate, last, dataOrder) {
      this.$emit("wenClearTag"); // 切换小眼睛就清空当前选择列表。选择的智能再当前一个聊天下
      if (contentId === null) { // 新会话的时候要清空
        const str = {
          contentId: '',
          lawInfoList: [], // 法规
          zhengQiuLawsList: [], // 征求法规
          answerInfoList: [], // 问答
          violateInfoList: [], // 违规案例
          dataOrder: []
        }
        if (this.getValue(clearFlag) !== '') {
          this.$emit("wenClickData", str); // 右侧数据为null展示
        }
        return;
      }
      this.contentId = contentId;
      this.question = question;
      _getMaterial(contentId).then(res => {
        this.$emit('wenStartChatLoading', false)
        if (res.data.success) {
          if (last === "last") { // 只赋值最后一个状态
            this.contentList[this.contentList.length - 1].status = 1;
          }
          // 转一遍json,就可以深度监听数组里面值的变化，要不最后一个小眼睛监听不到变化
          let contentListJson = JSON.stringify(this.contentList);
          this.contentList = JSON.parse(contentListJson);
          const listData = this.dataListHandle(res.data.result.lawsInfoList, res.data.result.answerList, res.data.result.violateList, res.data.result.solicitLawsList)
          let str = {
            contentId: contentId,
            dataOrder: dataOrder,
            lawInfoList: listData.lawsList, // 法规
            zhengQiuLawsList: listData.zhengQiuLawsList, // 征求法规
            answerInfoList: listData.qaList, // 问答
            violateInfoList: listData.violate // 违规案例
          }
          this.$emit("wenClickData", str, clearFlag, plate); // 右侧数据
        }
      });
    },
    // 去除字符串前后面空格
    trimStr(str) {
      return str.replace(/(^\s*)|(\s*$)/g, "");
    },
    // 处理返回的列表数据，空则默认是[]
    dataListHandle(law, qa, vio, zhengqiu) {
      let param = {
        lawsList: [],
        qaList: [],
        violate: [],
        zhengQiuLawsList: [],
      }
      if (law !== null) {
        param.lawsList = law;
      }
      if (qa !== null) {
        param.qaList = qa;
      }
      if (vio !== null) {
        param.violate = vio;
      }
      if (zhengqiu !== null) {
        param.zhengQiuLawsList = zhengqiu
      }
      return param;
    },

    /**
     * 判断值空
     * @param {value,type:String} val
     */
    getValue(val) {
      if (val === null || val === 'null' || val === undefined || val === 'undefined') {
        val = ''
      }
      return val
    },
    handleScroll(e) {
      const dom = this.$refs.contentRef;
      if (!dom) return;
      const scrollDistance = dom.scrollHeight - dom.scrollTop - dom.clientHeight;
      // 如果用户滚动到接近底部，重新启用自动滚动
      if (scrollDistance <= 30) {
        this.isUserScrolling = false;
      } else {
        // 用户向上滚动，停用自动滚动
        this.isUserScrolling = true;
      }
    },
    copyValue (v) {
      const el = document.createElement('input');
      el.setAttribute('value', v);
      document.body.appendChild(el);
      try {
        el.select();
        document.execCommand('copy');
        this.$message.success('复制成功');
      } catch (e) {
        this.$message.error('您的浏览器不支持，请手动复制');
      } finally {
        document.body.removeChild(el);
      }
    },
  },
  watch: {
    currentChat(newVal, oldVal) {
      if (this.wenNullList) {
        if (oldVal) {
          this.getChatContent();
          this.sendContent = '';
        }
      } else {
        if (newVal || oldVal === this.currentChat) {
          this.getChatContent();
          this.sendContent = '';
        }
      }
    }
  }
}
</script>
<style lang="scss">
@import "../../../styles/common.scss";

.stateInput .el-textarea__inner {
  min-height: 66px !important;
  height: 66px !important;
  padding-right: 105px;
}
.platform-session-icon {
  padding-top: 30px;
  position: absolute;
  right: 0px;
  bottom: -28px;
  display: none;
  padding-right: 12px;
  &:hover {
    display: flex;
  }
}
.basic-icon {
  cursor: pointer;
  display: flex;
  align-self: center;
  width: 60px;
  height: 24px;
  align-items: center;
  justify-content: center;
color: #666;
  i {
    font-size: 18px;
    color: #666 !important;
  }

  &:hover {
    background: #E0E7E9;
    border-radius: 4px;
  }
}

.WenWen {
  width: 100%;
  height: 100vh;
  z-index: 999;
  background-color: #F7F8FAFF;

  .zhenbie{
    color: gray;
    margin-left: 15px;
  }
  .hint {
    position: relative;
    z-index: 99;
  }

  :deep(.el-input--medium) {
    height: 34px;
    line-height: 34px;
    border-radius: 90px;
    border: 1px solid #FFFFFF99;
    color: #333333;
    padding: 0 10px;
  }

  .highlight {
    color: red;
  }

  .highlight_back {
    color: red;
    background-color: #bebaba;
  }

  .outer_frame {
    border: 1px solid #003cff;
    float: left;
    height: 100%
  }

  .el-input--medium .el-input__inner {
    height: 34px;
    line-height: 34px;
    border-radius: 90px;
    border: 1px solid #FFFFFF99;
    color: #333333;
    padding: 0 10px;
  }

  .button_tzsc {
    width: 100% !important;
    color: #CF1A1C !important;
    border: 1px solid #CF1A1C !important;
    text-align: center !important;
  }

  .button_ccsc {
    width: 100% !important;
    color: #CF1A1C !important;
    border: 1px solid #CF1A1C !important;
  }

  .r1Switch :hover {
    color: #CF1A1C;
  }

  .r1noSwitch :hover {
    background-color: #EEF9FF;
  }

  .new-dialog {
    cursor: pointer;
    //width: 108px;
    height: 34px;
    display: flex;
    align-items: center;
    padding: 4px 16px;
    margin-left: 12px;
    border-radius: 53px;
    border: 1px solid rgba(255, 255, 255, 1);
    box-sizing: border-box;
    background: rgba(255, 255, 255, 0.7);
    color: rgba(51, 51, 51, 1);
    font-size: 14px;
    line-height: 20px;
    margin-top: 2px;

    &.active {
      color: #CF1A1C;
      border: 1px solid var(--brand-414-bcf-5, #CF1A1C);
    }

    .operate {
      color: inherit;
      font-size: 14px;
      margin-right: 4px;
      vertical-align: middle;
    }

    &.disabled {
      cursor: not-allowed;
    }
  }

  .colCss {
    padding-left: 2px !important;
    padding-right: 2px !important;

    // 添加对el-button的样式覆盖
    .el-button {
      display: flex !important;
      align-items: center !important;
      justify-content: center !important;

      .iconfont {
        line-height: normal !important;
      }
    }
  }

  .colCss :hover {
    color: #CF1A1C !important;

    .iconfont {
      color: #CF1A1C !important;
    }
  }

  .platform-right-body {
    display: flex;
    flex-direction: column;
    height: calc(100% - 61px);

    .platform-right-content {
      flex: 1;
      overflow-y: auto;
      width: 100%;
      margin-bottom: 24px;

      .platform-right-chart {
        width: 100%;
        min-width: 300px;
        height: 270px;
      }

      .platform-right-container {
        //max-width:1200px;
        margin: 0 auto;
        padding: 32px 20px 0 30px;
        height: calc(100% - 48px);
      }

      .platform-session-content {
        //word-wrap: break-word;
        //word-break: break-all;
        //overflow: hidden;
        background: rgba(255, 255, 255, 0.9);
        border-radius: 10px;
        padding: 10px 16px;
        color: #333333;
        font-size: 14px;
        line-height: 24px;
        width: auto;
        margin-left: 12px;
        margin-bottom: 1px;
        position: relative;
        //white-space: pre-line;
        p {
          & + table {
            margin-top: 10px;
          }
        }

        table {
          border-spacing: inherit;
          border-top: 1px solid #efefef;
          border-right: 1px solid #efefef;

          thead {
            tr {
              background: #f5f7fa;
            }
          }

          & + p {
            margin-top: 14px;
          }

          th {
            color: #333;
            padding: 10px 6px;
            border-bottom: 1px solid #efefef;
            border-left: 1px solid #efefef;
          }

          td {
            color: #333;
            text-align: left !important;
            padding: 10px 6px;
            border-bottom: 1px solid #efefef;
            border-left: 1px solid #efefef;
          }

          tbody {
            tr {
              &:nth-child(2n+1) {
                background: #fff;
              }

              &:nth-child(2n) {
                background: #f5f7fa;
              }
            }
          }
        }

        &.question {
          background: #EEDCDC;
          box-shadow: 0 1px 1px rgba(138, 149, 153, 0.5);
          color: #072833;
          font-size: 16px;
          line-height: 24px;
          max-width: calc(100% - 90px);
        }

        &.answer {
          background: rgba(255, 255, 255, 0.9);
          box-shadow: 0px 1px 3px 0px rgba(164, 171, 174, 0.50);
          line-height: 24px;
          border-radius: 10px;
        }

        &:hover .platform-session-icon {
          display: flex;
        }
      }

      .associate_question {
        margin-top: 10px;
        color: #969899;
        font-size: 12px;
        font-style: normal;
        font-weight: 400;
        line-height: 18px;
        margin-left: 12px;
      }

      .associate_question_content {
        padding: 5px 8px;
        cursor: pointer !important;
        border-radius: 5px;
        display: inline-block;
        background: rgba(255, 255, 255, 0.9);
        margin-right: 10px;
        margin-bottom: 10px;
      }

      .platform-session-item__title {
        font-size: 16px;
        line-height: 22px;
        margin-bottom: 8px;
        font-weight: 600;
      }

      .question_time {
        position: relative;
        width: 100%;
        margin-top: 24px;
        margin-bottom: -16px;
        text-align: center;

        .question_time_data {
          color: #485359;
          margin: 0 auto;
          text-align: center;
          background: #EBEBEB;
          padding: 2px 8px;
          border-radius: 3px;
          justify-content: space-between;
          align-items: center;
          display: inline-block;
        }
      }

      .platform-session-item {
        display: flex;
        padding-right: 67px;
        margin-top: 33px;

        .platform-session-item__name {
          width: 30px;
          height: 30px;
          margin-left: 12px;
          background: #CF1A1C;
          border-radius: 6px;
          color: #FFFFFF;
          font-size: 18px;
          text-align: center;
          line-height: 30px;
        }

        .platform-session-item__img {
          img {
            width: 30px;
          }
        }

        &.flex-row-reverse {
          flex-direction: row-reverse;
          padding-right: 0;
          padding-left: 97px;

          .platform-session-item__img {
            margin-left: 12px;
          }
        }

      }

      .platform-session-question {
        font-size: 14px;
        color: #666666;
        line-height: 24px;
        padding: 20px 0 0 12px;
      }

      .platform-session-question__item {
        display: inline-block;
        background: rgba(255, 255, 255, 0.6);
        border-radius: 8px;
        font-size: 14px;
        line-height: 24px;
        color: #333333;
        margin-left: 12px;
        padding: 8px 16px;
        margin-top: 8px;
        cursor: pointer;
      }

      .platform-session-questionList {
        padding-left: 30px;
        margin-bottom: 24px;
      }

      .platform-session-copy {
        padding: 3px;
        display: inline;
        margin-left: 8px;
        border-radius: 36px;
        height: 22px;
        text-align: center;
        margin-top: 10px;
        cursor: pointer;

        .iconfont {
          color: #333;
          font-size: 14px;
        }

        &:hover {
          background: rgba(204, 204, 204, 0.2);
        }
      }
    }

    .platform-right-footer {
      .platform-footer-body {
        //max-width: 1200px;
        padding: 0 20px 24px;
        margin: 0 auto;
      }

      .platform-right-footer__item {
        display: inline-block;
        background: rgba(208, 235, 255, 0.9);
        border-radius: 6px;
        padding: 4px 16px;
        margin-left: 8px;
        font-size: 14px;
        line-height: 24px;
        color: #CF1A1C;
        cursor: pointer;
      }

      .platform-right-footer__moreQuestion {
        color: #CF1A1C;
        font-size: 14px;
      }

      .platform-right-footer__button {
        color: #CF1A1C;
        font-size: 14px;
        padding: 5px 14px;
        border: 1px solid #CF1A1C;
        border-radius: 23px;
        display: inline-block;
        cursor: pointer;
      }

      .platform-footer-send {
        background: rgba(255, 255, 255, 0.95);
        box-shadow: 0 1px 2px rgba(65, 65, 65, 0.3);
        border-radius: 26px;
        padding: 12px 16px;
        overflow: hidden;
        flex: 1;
        transition: all 0.3s;

        .platform-footer-send__textarea {
          flex: 1;
          margin-right: 8px;
        }

        &.focus {
          .platform-footer-send__number {
            display: block;
          }
        }

        &.focus {
          min-height: 98px !important;

        }

        &.empty {
          min-height: 54px;
          height: 54px;
        }

        .platform-footer-send__content {
          display: flex;
          //align-items: center;
        }

        .platform-footer-send__number {
          color: #666666;
          font-size: 14px;
          line-height: 22px;
          margin-top: 8px;
        }

        textarea {
          border: none;
          outline: none;
          flex: 1;
          margin-right: 16px;
          color: #111;
          height: 31px !important;
          resize: none;
          padding-left: 0;
          padding-right: 0;
          font-family: "Helvetica Neue", Helvetica, "PingFang SC", "Hiragino Sans GB", "Microsoft YaHei", "微软雅黑", Arial, sans-serif;
          overflow-y: scroll;
        }

        textarea::-webkit-input-placeholder {
          color: #999999;
          font-size: 14px;
          font-family: "Helvetica Neue", Helvetica, "PingFang SC", "Hiragino Sans GB", "Microsoft YaHei", "微软雅黑", Arial, sans-serif;
        }

        textarea::-moz-placeholder {
          color: #999999;
          font-size: 14px;
          font-family: "Helvetica Neue", Helvetica, "PingFang SC", "Hiragino Sans GB", "Microsoft YaHei", "微软雅黑", Arial, sans-serif;
        }

        textarea:-moz-placeholder {
          color: #999999;
          font-size: 14px;
          font-family: "Helvetica Neue", Helvetica, "PingFang SC", "Hiragino Sans GB", "Microsoft YaHei", "微软雅黑", Arial, sans-serif;
        }

        textarea:-ms-input-placeholder {
          color: #999999;
          font-size: 14px;
          font-family: "Helvetica Neue", Helvetica, "PingFang SC", "Hiragino Sans GB", "Microsoft YaHei", "微软雅黑", Arial, sans-serif;
        }

        .platform-footer-send__button {
          border-radius: 23px;
          height: 38px;
          line-height: 38px;
          width: 85px;
          text-align: center;
          color: #FFFFFF;
          font-size: 14px;
          cursor: pointer;
          margin-top: -4px;
          background-color: rgba(207, 26, 28, 1);

          &.disabled {
            //background: #D0F2FD;
            background: #fae1e1;
            cursor: default;

            .iconfont {
              cursor: default;
            }
          }

          .iconfont {
            color: #fff;
            margin-right: 6px;
            font-size: 14px;
          }
        }
      }

      .platform-footer__set {
        border-radius: 32px;
        width: 36px;
        height: 36px;
        cursor: pointer;
        display: inline-block;
        line-height: 36px;
        text-align: center;

        &:hover {
          background: rgba(51, 51, 51, 0.05);
        }

        .iconfont {
          font-size: 16px;
          color: #777;
        }
      }

      .platform-footer-main {
        z-index: 99;
        position: relative;
        //display: flex;
        align-items: center;
        min-height: 52px;
      }
    }
  }

  .platform-alert {
    cursor: pointer;

    .el-alert {
      //padding: 8px 20px;
      height: 32px;

      .el-alert__content {
        width: 100%;
      }
    }

    .el-alert.is-light .el-alert__closebtn {
      line-height: 13px;
    }

    .el-alert__description {
      margin-top: 0;
      color: #333;
      overflow: hidden;
      margin-right: 10px;
      position: relative;
      line-height: 13px;

      .iconfont {
        color: #ff7d00;
        margin-right: 5px;
        vertical-align: middle;
        position: absolute;
        z-index: 1;
        //top:1px;
        width: 26px;
        background: #fff2e6;
      }
    }

    .platform-alert__text {
      white-space: nowrap;
      margin-left: 25px;
    }
  }

  .el-button--info.is-plain {
    color: #333333;
    background: #FFF;
    background: rgba(255, 255, 255, 0.60);
    border-radius: 25px;
    cursor: pointer;
    border: 1px solid #FFFFFF99;
  }

  .el-button--info.is-plain:hover {
    border: 1px solid var(--brand-414-bcf-5, #FFC4C4);
    background: rgba(255, 255, 255, 0.90);
  }

  .platform-right-header__set {
    width: 30px;
    height: 30px;
    display: inline-block;
    line-height: 30px;
    border-radius: 30px;
    cursor: pointer;
    text-align: center;
    margin-top: 3px;
    margin-left: 2px;

    .iconfont {
      color: #777777;
      font-size: 18px;
    }

    &:hover {
      background: #FFFFFF7F;

      .iconfont {
        color: #CF1A1C;
      }
    }
  }

  .platform-session-anniu {
    color: #666666;
    display: -ms-flexbox;
    display: flow;
    position: relative;
    height: 30px;
    margin-top: 10px;
    width: 100%;
    margin-right: 0px;
    text-align: right;

    .iconfont {
      font-size: 10px;
    }
  }

  .anniu_title {
    position: relative;
    top: -2px;
    font-size: 14px;
    margin-left: 6px;
  }

  .zan_css {
    float: right;
    cursor: pointer;
    padding: 5px 6px;
    border-radius: 8px;
  }

  .zan_css :hover {
    color: #CF1A1C;
  }

  .cai_css {
    float: right;
    padding: 5px 6px;
    cursor: pointer;
    //background: var(--f-5-f-6-f-8, #F5F6F8);
    border-radius: 8px;
  }

  .cai_css :hover {
    color: #CF1A1C;
  }

  .fz_css {
    float: right;
    padding: 5px 6px;
    cursor: pointer;
    //background: var(--f-5-f-6-f-8, #F5F6F8);
    border-radius: 8px;
  }

  .fz_css :hover {
    color: #CF1A1C;
  }

  .share_css {
    float: right;
    padding: 5px 6px;
    cursor: pointer;
    //background: var(--f-5-f-6-f-8, #F5F6F8);
    border-radius: 8px;
  }

  .share_css :hover {
    color: #CF1A1C;
  }

  .ck_css {
    padding: 5px 6px;
    cursor: pointer;
    height: 31px;
    //background: var(--f-5-f-6-f-8, #F5F6F8);
    border-radius: 8px;
    position: absolute;
    right: -39px;
    top: 0px;
  }

  .contentCss:hover .platform-session-anniu {
    //visibility: visible;
    //display: flex;
  }

  .huihua_don {
    display: inline-block;
    width: 4.57px;
    height: 4.57px;
    background: rgba(102, 102, 102, 0.3);
    border-radius: 100%;
    margin: 0 3px;

    &.dot1 {
      animation: dotA 1.5s infinite step-start both;
    }

    &.dot2 {
      animation: dotB 1.5s infinite step-start both;
    }

    &.dot3 {
      animation: dotC 1.5s infinite step-start both;
    }
  }

  @keyframes dotA {
    33% {
      background: #666666;
    }
    66% {
      background: #666666;
    }
    99% {
      background: #666666;
    }
  }
  @keyframes dotB {
    33% {
      background: rgba(102, 102, 102, 0.3);
    }
    66% {
      background: #666666;
    }
    99% {
      background: #666666;
    }
  }
  @keyframes dotC {
    33% {
      background: rgba(102, 102, 102, 0.3);
    }
    66% {
      background: rgba(102, 102, 102, 0.3);
    }
    99% {
      background: #666666;
    }
  }

  .answerTip {
    max-width: 80%;
    height: 0;
    color: #969899;
    font-size: 13px;
    font-style: normal;
    font-weight: 400;
    line-height: 18px; /* 184.615% */
    position: relative;
    left: 48px;
  }

  .fontOver {
    word-wrap: break-word;
    word-break: break-all;
    overflow: hidden;
    white-space: pre-wrap;
  }

  .answerTableCla {
    margin-top: 16px;

    .cell {
      white-space: pre-wrap;
    }

    .el-table__header-wrapper {
      width: 99.5% !important;
    }

    table thead th {
      background-color: #E8F2FC;
    }

    .ellipsis-container {
      display: -webkit-box;
      -webkit-box-orient: vertical;
      overflow: hidden;
      -webkit-line-clamp: 2; /* 控制显示的行数 */
    }
  }

  .answerScrollTableCla {
    border-radius: 8px;
  }

  .tableActCla {
    margin-bottom: 6px;
    display: flex;
    justify-content: flex-end;

    div {
      cursor: pointer;
    }

    div {
      padding: 2px 4px;
      border-radius: 3px;
      gap: 4px;
      color: #666666;
    }

    i {
      font-size: 14px;
      color: #666666;
    }

    div:hover {
      background: #F5F6F8;
      color: #333333;
    }

    i:hover {
      background: #F5F6F8;
      color: #333333;
    }
  }

  .color-99 {
    color: #999999 !important;
  }
}

p {
  margin: 0;
}

.thinking-header {
  display: flex;
  align-items: center;
  cursor: pointer;
  margin-bottom: 8px;

  .thinking-icon {
    width: 16px;
    height: 16px;
    margin-left: 8px;
    transition: transform 0.3s;

    &.expanded {
      transform: rotate(90deg);
    }
  }

  .thinking-title {
    font-weight: 500;
    font-size: 14px;
  }
}

.thinking-content {
  background: #F7F8FA;
  border: 1px solid #E5E6EB;
  color: #666666;
  font-size: 14px;
  padding: 12px 16px;
  border-radius: 6px;
  margin-bottom: 8px;

  &.collapsed {
    display: none;
  }
}

</style>
