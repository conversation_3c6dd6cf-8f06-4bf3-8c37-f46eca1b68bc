<!--
  *@name wenwenFeedback.vue
  *<AUTHOR>
  *@date 2025/7/29 10:38
-->
<template>
  <el-dialog
    title="反馈"
    :visible.sync="visible"
    width="500px"
    :close-on-click-modal="false"
    :close-on-press-escape="false"
    @close="handleClose"
  >
    <div class="feedback-form">
      <!-- 反馈类型 -->
      <div class="form-item">
        <label class="form-label">反馈类型：</label>
        <div class="radio-group">
          <el-radio v-model="feedbackForm.type" label="回答有误">回答有误</el-radio>
          <el-radio v-model="feedbackForm.type" label="响应慢">响应慢</el-radio>
          <el-radio v-model="feedbackForm.type" label="条例有误">条例有误</el-radio>
          <el-radio v-model="feedbackForm.type" label="法规有误">法规有误</el-radio>
          <el-radio v-model="feedbackForm.type" label="其它">其它</el-radio>
        </div>
      </div>

      <!-- 用户姓名 -->
      <div class="form-item">
        <label class="form-label">用户姓名：</label>
        <el-input
          v-model="feedbackForm.userName"
          placeholder="请输入用户姓名"
          class="form-input"
        />
      </div>

      <!-- 联系方式 -->
      <div class="form-item">
        <label class="form-label">联系方式：</label>
        <el-input
          v-model="feedbackForm.contact"
          placeholder="请输入联系方式"
          class="form-input"
        />
      </div>

      <!-- 反馈内容 -->
      <div class="form-item">
        <label class="form-label">反馈内容：</label>
        <el-input
          v-model="feedbackForm.content"
          type="textarea"
          :rows="4"
          placeholder="请输入反馈内容..."
          maxlength="1000"
          show-word-limit
          class="form-textarea"
        />
      </div>
    </div>

    <div slot="footer" class="dialog-footer">
      <el-button @click="handleDirectDislike" type="text" class="direct-dislike-btn">
        直接踩
      </el-button>
      <el-button @click="handleClose">取消</el-button>
      <el-button type="primary" @click="handleSubmit">提交反馈</el-button>
    </div>
  </el-dialog>
</template>

<script>
export default {
  name: "wenwenFeedback",
  props: {
    visible: {
      type: Boolean,
      default: false
    },
    feedbackData: {
      type: Object,
      default: () => ({})
    }
  },
  data() {
    return {
      feedbackForm: {
        type: '回答有误',
        userName: '',
        contact: '',
        content: ''
      }
    }
  },
  watch: {
    visible(newVal) {
      if (newVal) {
        this.resetForm()
      }
    }
  },
  methods: {
    // 重置表单
    resetForm() {
      this.feedbackForm = {
        type: '回答有误',
        userName: '',
        contact: '',
        content: ''
      }
    },

    // 关闭弹窗
    handleClose() {
      this.$emit('close')
    },

    // 提交反馈
    handleSubmit() {
      // 这里可以添加表单验证
      if (!this.feedbackForm.content.trim()) {
        this.$message.warning('请输入反馈内容')
        return
      }

      // 提交反馈数据
      this.$emit('submit-feedback', {
        ...this.feedbackForm,
        ...this.feedbackData
      })

      this.handleClose()
    },

    // 直接踩
    handleDirectDislike() {
      this.$emit('direct-dislike', this.feedbackData)
      this.handleClose()
    }
  }
}
</script>

<style scoped lang="scss">
.feedback-form {
  .form-item {
    margin-bottom: 20px;

    .form-label {
      display: inline-block;
      width: 80px;
      font-weight: 500;
      color: #333;
      vertical-align: top;
      line-height: 32px;
    }

    .radio-group {
      display: inline-block;
      width: calc(100% - 80px);

      .el-radio {
        margin-right: 15px;
        margin-bottom: 8px;
      }
    }

    .form-input {
      width: calc(100% - 80px);
    }

    .form-textarea {
      width: calc(100% - 80px);
    }
  }
}

.dialog-footer {
  text-align: right;

  .direct-dislike-btn {
    color: #CF1A1C;
    margin-right: auto;
    float: left;
  }
}
</style>